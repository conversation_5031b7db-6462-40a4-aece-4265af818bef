{"name": "file-transfer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview --host --port $PORT", "start": "vite preview --host --port $PORT"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/forms": "^0.5.9", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "i18next": "^24.2.0", "i18next-browser-languagedetector": "^8.0.2", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.0", "vite": "^5.0.0", "tailwindcss": "^3.0.0", "serve-static": "^1.15.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0"}}